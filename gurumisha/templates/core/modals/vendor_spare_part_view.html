<!-- Vendor View Spare Part Modal -->
<div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
     x-data="{ show: true }"
     x-show="show"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     @click.self="show = false; setTimeout(() => $el.remove(), 200)">
    
    <div class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
         x-show="show"
         x-transition:enter="transition ease-out duration-300 transform"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-200 transform"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95">
        
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-harrier-dark to-gray-800 px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-eye text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-white font-montserrat">Spare Part Details</h3>
                        <p class="text-white text-opacity-80 text-sm font-raleway">{{ spare_part.name }}</p>
                    </div>
                </div>
                <button type="button"
                        class="text-white hover:text-gray-200 transition-colors p-2 hover:bg-white hover:bg-opacity-10 rounded-lg"
                        @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        
        <!-- Modal Body -->
        <div class="bg-gradient-to-br from-gray-50 to-white px-6 py-6 max-h-[70vh] overflow-y-auto modal-body">
            
            <!-- Part Image and Basic Info -->
            <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Part Image -->
                    <div class="md:col-span-1">
                        {% if spare_part.main_image %}
                            <img src="{{ spare_part.main_image.url }}" alt="{{ spare_part.name }}" 
                                 class="w-full h-48 object-cover rounded-lg border border-gray-200 shadow-sm">
                        {% else %}
                            <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg border border-gray-200 flex items-center justify-center">
                                <i class="fas fa-cog text-6xl text-gray-400"></i>
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Basic Information -->
                    <div class="md:col-span-2 space-y-4">
                        <div>
                            <h4 class="text-2xl font-bold text-harrier-dark font-montserrat">{{ spare_part.name }}</h4>
                            <p class="text-gray-600 font-raleway">{{ spare_part.category_new.name|default:"Uncategorized" }}</p>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm font-bold text-gray-500 font-montserrat">SKU</p>
                                <p class="text-lg font-semibold text-harrier-dark font-raleway">{{ spare_part.sku }}</p>
                            </div>
                            <div>
                                <p class="text-sm font-bold text-gray-500 font-montserrat">Part Number</p>
                                <p class="text-lg font-semibold text-harrier-dark font-raleway">{{ spare_part.part_number|default:"N/A" }}</p>
                            </div>
                            <div>
                                <p class="text-sm font-bold text-gray-500 font-montserrat">Condition</p>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    {% if spare_part.condition == 'new' %}bg-green-100 text-green-800
                                    {% elif spare_part.condition == 'used' %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-blue-100 text-blue-800{% endif %}">
                                    {{ spare_part.get_condition_display }}
                                </span>
                            </div>
                            <div>
                                <p class="text-sm font-bold text-gray-500 font-montserrat">Unit</p>
                                <p class="text-lg font-semibold text-harrier-dark font-raleway">{{ spare_part.get_unit_display }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Pricing Information -->
            <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-6">
                <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                    <i class="fas fa-dollar-sign mr-2 text-green-600"></i>Pricing Information
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                        <p class="text-sm font-bold text-green-600 font-montserrat">Selling Price</p>
                        <p class="text-2xl font-bold text-green-700 font-raleway">KSh {{ spare_part.price|floatformat:0 }}</p>
                    </div>
                    
                    {% if spare_part.cost_price %}
                    <div class="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <p class="text-sm font-bold text-blue-600 font-montserrat">Cost Price</p>
                        <p class="text-2xl font-bold text-blue-700 font-raleway">KSh {{ spare_part.cost_price|floatformat:0 }}</p>
                    </div>
                    {% endif %}
                    
                    {% if spare_part.discount_price %}
                    <div class="text-center p-4 bg-red-50 rounded-lg border border-red-200">
                        <p class="text-sm font-bold text-red-600 font-montserrat">Discount Price</p>
                        <p class="text-2xl font-bold text-red-700 font-raleway">KSh {{ spare_part.discount_price|floatformat:0 }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Inventory Information -->
            <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-6">
                <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                    <i class="fas fa-boxes mr-2 text-blue-600"></i>Inventory Information
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="text-center p-4 bg-gray-50 rounded-lg border border-gray-200">
                        <p class="text-sm font-bold text-gray-600 font-montserrat">Current Stock</p>
                        <p class="text-2xl font-bold text-harrier-dark font-raleway">{{ spare_part.stock_quantity }}</p>
                    </div>
                    
                    {% if spare_part.minimum_stock %}
                    <div class="text-center p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                        <p class="text-sm font-bold text-yellow-600 font-montserrat">Minimum Stock</p>
                        <p class="text-2xl font-bold text-yellow-700 font-raleway">{{ spare_part.minimum_stock }}</p>
                    </div>
                    {% endif %}
                    
                    {% if spare_part.maximum_stock %}
                    <div class="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                        <p class="text-sm font-bold text-purple-600 font-montserrat">Maximum Stock</p>
                        <p class="text-2xl font-bold text-purple-700 font-raleway">{{ spare_part.maximum_stock }}</p>
                    </div>
                    {% endif %}
                    
                    <div class="text-center p-4 
                        {% if spare_part.is_in_stock %}bg-green-50 border-green-200{% else %}bg-red-50 border-red-200{% endif %} 
                        rounded-lg border">
                        <p class="text-sm font-bold 
                            {% if spare_part.is_in_stock %}text-green-600{% else %}text-red-600{% endif %} 
                            font-montserrat">Status</p>
                        <p class="text-lg font-bold 
                            {% if spare_part.is_in_stock %}text-green-700{% else %}text-red-700{% endif %} 
                            font-raleway">
                            {% if spare_part.is_in_stock %}In Stock{% else %}Out of Stock{% endif %}
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Description and Specifications -->
            {% if spare_part.description or spare_part.specifications %}
            <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-6">
                <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                    <i class="fas fa-file-alt mr-2 text-purple-600"></i>Description & Specifications
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {% if spare_part.description %}
                    <div>
                        <h5 class="text-md font-bold text-gray-700 mb-2 font-montserrat">Description</h5>
                        <p class="text-gray-600 font-raleway leading-relaxed">{{ spare_part.description }}</p>
                    </div>
                    {% endif %}
                    
                    {% if spare_part.specifications %}
                    <div>
                        <h5 class="text-md font-bold text-gray-700 mb-2 font-montserrat">Specifications</h5>
                        <p class="text-gray-600 font-raleway leading-relaxed">{{ spare_part.specifications }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
            
            <!-- Additional Information -->
            <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-6">
                <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                    <i class="fas fa-info mr-2 text-gray-600"></i>Additional Information
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <p class="text-sm font-bold text-gray-500 font-montserrat">Supplier</p>
                        <p class="text-lg font-semibold text-harrier-dark font-raleway">{{ spare_part.supplier.name|default:"No supplier assigned" }}</p>
                    </div>
                    
                    {% if spare_part.barcode %}
                    <div>
                        <p class="text-sm font-bold text-gray-500 font-montserrat">Barcode</p>
                        <p class="text-lg font-semibold text-harrier-dark font-raleway">{{ spare_part.barcode }}</p>
                    </div>
                    {% endif %}
                    
                    <div>
                        <p class="text-sm font-bold text-gray-500 font-montserrat">Created</p>
                        <p class="text-lg font-semibold text-harrier-dark font-raleway">{{ spare_part.created_at|date:"M d, Y" }}</p>
                    </div>
                    
                    <div>
                        <p class="text-sm font-bold text-gray-500 font-montserrat">Last Updated</p>
                        <p class="text-lg font-semibold text-harrier-dark font-raleway">{{ spare_part.updated_at|date:"M d, Y" }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Recent Stock Movements -->
            {% if recent_movements %}
            <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-6">
                <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                    <i class="fas fa-history mr-2 text-orange-600"></i>Recent Stock Movements
                </h4>
                
                <div class="space-y-3">
                    {% for movement in recent_movements %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 rounded-full flex items-center justify-center
                                {% if movement.movement_type == 'initial' %}bg-blue-100 text-blue-600
                                {% elif 'increase' in movement.movement_type %}bg-green-100 text-green-600
                                {% elif 'decrease' in movement.movement_type %}bg-red-100 text-red-600
                                {% else %}bg-gray-100 text-gray-600{% endif %}">
                                <i class="fas fa-{% if movement.movement_type == 'initial' %}plus{% elif 'increase' in movement.movement_type %}arrow-up{% elif 'decrease' in movement.movement_type %}arrow-down{% else %}exchange-alt{% endif %} text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-semibold text-harrier-dark font-montserrat">{{ movement.get_movement_type_display }}</p>
                                <p class="text-xs text-gray-500 font-raleway">{{ movement.created_at|date:"M d, Y H:i" }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-bold text-harrier-dark font-raleway">{{ movement.quantity }} units</p>
                            {% if movement.notes %}
                            <p class="text-xs text-gray-500 font-raleway">{{ movement.notes|truncatechars:30 }}</p>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
                <button onclick="openEditPartModal({{ spare_part.id }})" 
                        class="w-full sm:w-auto px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl font-bold hover:from-blue-700 hover:to-blue-800 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl font-montserrat">
                    <i class="fas fa-edit mr-2"></i>Edit Part
                </button>
                <button type="button"
                        class="w-full sm:w-auto px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-bold hover:bg-gray-200 transition-all duration-200 font-montserrat"
                        @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                    <i class="fas fa-times mr-2"></i>Close
                </button>
            </div>
        </div>
    </div>
</div>
